<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <h1>登录功能集成测试</h1>
    <p>测试修改后的登录功能，验证实际API集成和永久Cookie设置</p>
    
    <div class="test-container">
        <h2>环境检查</h2>
        <button onclick="checkEnvironment()">检查环境</button>
        <div id="environmentResult"></div>
    </div>

    <div class="test-container">
        <h2>实际登录API测试</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="testUsername" value="bydq_admin">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="testPassword" value="Aa123456">
        </div>
        <button onclick="testRealLogin()">测试实际登录API</button>
        <button onclick="testInvalidCredentials()">测试错误凭据</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-container">
        <h2>Cookie永久性测试</h2>
        <button onclick="testPermanentCookie()">测试永久Cookie设置</button>
        <button onclick="checkCookieExpiry()">检查Cookie过期时间</button>
        <button onclick="clearTestCookies()">清除测试Cookie</button>
        <div id="cookieResult"></div>
    </div>

    <div class="test-container">
        <h2>Token验证测试</h2>
        <button onclick="testTokenValidation()">测试Token验证</button>
        <button onclick="testApiWithToken()">测试API调用</button>
        <div id="tokenResult"></div>
    </div>

    <div class="test-container">
        <h2>完整集成测试</h2>
        <button onclick="runFullIntegrationTest()">运行完整集成测试</button>
        <div id="integrationResult"></div>
    </div>

    <!-- 引入config.js -->
    <script src="config.js"></script>
    
    <script>
        // 显示结果的辅助函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const statusClass = `status-${type}`;
            const timestamp = new Date().toLocaleTimeString();
            
            element.innerHTML = `
                <div class="test-result ${type}">
                    <span class="status-indicator ${statusClass}"></span>
                    [${timestamp}] ${message}
                </div>
            ` + element.innerHTML;
        }

        // 环境检查
        function checkEnvironment() {
            showResult('environmentResult', '开始检查环境...', 'info');
            
            const requiredFunctions = [
                'loginUser', 'getTokenFromCookies', 'updateAuthToken', 
                'getAlertDeviceList', 'makeHttpRequest', 'NETWORK_CONFIG'
            ];
            
            const results = [];
            const missing = [];
            
            requiredFunctions.forEach(func => {
                if (typeof window[func] !== 'undefined') {
                    results.push(`✅ ${func}: 已加载`);
                } else {
                    results.push(`❌ ${func}: 未找到`);
                    missing.push(func);
                }
            });
            
            // 检查网络配置
            if (window.NETWORK_CONFIG) {
                results.push(`✅ API基础URL: ${NETWORK_CONFIG.API_BASE_URL}`);
            }
            
            const summary = missing.length === 0 
                ? '✅ 环境检查通过，所有必需函数已加载'
                : `❌ 环境检查失败，缺少函数: ${missing.join(', ')}`;
            
            showResult('environmentResult', 
                `${summary}\n\n详细结果:\n${results.join('\n')}`, 
                missing.length === 0 ? 'success' : 'error'
            );
        }

        // 测试实际登录API
        async function testRealLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            showResult('loginResult', `开始测试实际登录API - 用户名: ${username}`, 'info');
            
            try {
                if (typeof loginUser !== 'function') {
                    throw new Error('loginUser函数未找到，请检查config.js是否正确加载');
                }
                
                const result = await loginUser(username, password, '', 1);
                
                if (result.success) {
                    showResult('loginResult', 
                        `✅ 登录成功!\n` +
                        `Token长度: ${result.token.length}\n` +
                        `Token前缀: ${result.token.substring(0, 30)}...\n` +
                        `消息: ${result.message}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', 
                        `❌ 登录失败: ${result.message}`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('loginResult', 
                    `❌ 登录异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 测试错误凭据
        async function testInvalidCredentials() {
            showResult('loginResult', '测试错误凭据...', 'info');
            
            try {
                const result = await loginUser('wrong_user', 'wrong_pass', '', 1);
                
                if (!result.success) {
                    showResult('loginResult', 
                        `✅ 错误凭据测试通过: ${result.message}`, 
                        'success'
                    );
                } else {
                    showResult('loginResult', 
                        `❌ 错误凭据测试失败: 应该登录失败但却成功了`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('loginResult', 
                    `❌ 错误凭据测试异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 测试永久Cookie设置
        async function testPermanentCookie() {
            showResult('cookieResult', '测试永久Cookie设置...', 'info');
            
            try {
                // 先登录获取真实Token
                const loginResult = await loginUser('bydq_admin', 'Aa123456', '', 1);
                
                if (!loginResult.success) {
                    throw new Error('无法获取有效Token进行Cookie测试');
                }
                
                // 检查Cookie是否被正确设置
                const cookieToken = getTokenFromCookies();
                
                if (cookieToken) {
                    // 分析Cookie属性
                    const cookieString = document.cookie;
                    const hasExpires = cookieString.includes('expires=');
                    const hasMaxAge = cookieString.includes('max-age=');
                    
                    showResult('cookieResult', 
                        `✅ Cookie设置成功\n` +
                        `Token匹配: ${cookieToken === loginResult.token ? '是' : '否'}\n` +
                        `包含expires属性: ${hasExpires ? '是' : '否'}\n` +
                        `包含max-age属性: ${hasMaxAge ? '是' : '否'}\n` +
                        `Cookie字符串长度: ${cookieString.length}`, 
                        'success'
                    );
                } else {
                    showResult('cookieResult', 
                        `❌ Cookie设置失败，无法读取Admin-Token`, 
                        'error'
                    );
                }
            } catch (error) {
                showResult('cookieResult', 
                    `❌ Cookie测试异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 检查Cookie过期时间
        function checkCookieExpiry() {
            showResult('cookieResult', '检查Cookie过期时间...', 'info');
            
            const cookieString = document.cookie;
            const adminTokenMatch = cookieString.match(/Admin-Token=([^;]+)/);
            
            if (adminTokenMatch) {
                // 尝试解析过期时间
                const expiresMatch = cookieString.match(/expires=([^;]+)/);
                
                if (expiresMatch) {
                    const expiryDate = new Date(expiresMatch[1]);
                    const now = new Date();
                    const yearsFromNow = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 365);
                    
                    showResult('cookieResult', 
                        `✅ Cookie过期时间分析\n` +
                        `过期时间: ${expiryDate.toLocaleString()}\n` +
                        `距离现在: ${yearsFromNow.toFixed(1)} 年\n` +
                        `是否为长期有效: ${yearsFromNow > 5 ? '是' : '否'}`, 
                        yearsFromNow > 5 ? 'success' : 'warning'
                    );
                } else {
                    showResult('cookieResult', 
                        `⚠️ Cookie中未找到expires属性，可能为会话Cookie`, 
                        'warning'
                    );
                }
            } else {
                showResult('cookieResult', 
                    `❌ 未找到Admin-Token Cookie`, 
                    'error'
                );
            }
        }

        // 清除测试Cookie
        function clearTestCookies() {
            document.cookie = 'Admin-Token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
            showResult('cookieResult', '✅ 测试Cookie已清除', 'success');
        }

        // 测试Token验证
        async function testTokenValidation() {
            showResult('tokenResult', '测试Token验证...', 'info');
            
            try {
                const token = getTokenFromCookies();
                
                if (!token) {
                    showResult('tokenResult', '❌ 未找到Token，请先登录', 'error');
                    return;
                }
                
                // 通过API调用验证Token
                const result = await getAlertDeviceList({ pageSize: 1 });
                
                showResult('tokenResult', 
                    `Token验证结果: ${result.success ? '✅ 有效' : '❌ 无效'}\n` +
                    `API响应: ${result.message}\n` +
                    `Token长度: ${token.length}`, 
                    result.success ? 'success' : 'error'
                );
            } catch (error) {
                showResult('tokenResult', 
                    `❌ Token验证异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 测试API调用
        async function testApiWithToken() {
            showResult('tokenResult', '测试API调用...', 'info');
            
            try {
                const alertResult = await getAlertDeviceList({ pageSize: 3 });
                const deviceResult = await getDeviceRunningStatus(298);
                
                showResult('tokenResult', 
                    `报警列表API: ${alertResult.success ? '✅ 成功' : '❌ 失败'}\n` +
                    `设备状态API: ${deviceResult.success ? '✅ 成功' : '❌ 失败'}\n` +
                    `报警数据条数: ${alertResult.data ? alertResult.data.length : 0}\n` +
                    `设备数据: ${deviceResult.data ? '已获取' : '未获取'}`, 
                    (alertResult.success && deviceResult.success) ? 'success' : 'error'
                );
            } catch (error) {
                showResult('tokenResult', 
                    `❌ API调用异常: ${error.message}`, 
                    'error'
                );
            }
        }

        // 运行完整集成测试
        async function runFullIntegrationTest() {
            showResult('integrationResult', '🚀 开始完整集成测试...', 'info');
            
            const testResults = [];
            
            try {
                // 1. 环境检查
                const requiredFunctions = ['loginUser', 'getTokenFromCookies', 'updateAuthToken'];
                const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');
                testResults.push(`环境检查: ${missingFunctions.length === 0 ? '✅ 通过' : '❌ 失败'}`);
                
                if (missingFunctions.length > 0) {
                    throw new Error(`缺少必需函数: ${missingFunctions.join(', ')}`);
                }
                
                // 2. 清除现有Cookie
                document.cookie = 'Admin-Token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
                testResults.push('Cookie清理: ✅ 完成');
                
                // 3. 执行登录
                const loginResult = await loginUser('bydq_admin', 'Aa123456', '', 1);
                testResults.push(`登录测试: ${loginResult.success ? '✅ 成功' : '❌ 失败'}`);
                
                if (!loginResult.success) {
                    throw new Error(`登录失败: ${loginResult.message}`);
                }
                
                // 4. 验证Cookie设置
                await new Promise(resolve => setTimeout(resolve, 100)); // 等待Cookie写入
                const cookieToken = getTokenFromCookies();
                testResults.push(`Cookie设置: ${cookieToken ? '✅ 成功' : '❌ 失败'}`);
                
                // 5. 验证Token有效性
                const apiResult = await getAlertDeviceList({ pageSize: 1 });
                testResults.push(`Token验证: ${apiResult.success ? '✅ 有效' : '❌ 无效'}`);
                
                // 6. 检查Cookie永久性
                const cookieString = document.cookie;
                const hasLongExpiry = cookieString.includes('expires=') && 
                    new Date(cookieString.match(/expires=([^;]+)/)?.[1] || '').getFullYear() > new Date().getFullYear() + 5;
                testResults.push(`Cookie永久性: ${hasLongExpiry ? '✅ 长期有效' : '⚠️ 可能为会话Cookie'}`);
                
                // 生成最终报告
                const allPassed = testResults.every(result => result.includes('✅'));
                const summary = allPassed ? '🎉 所有测试通过！' : '⚠️ 部分测试未通过';
                
                showResult('integrationResult', 
                    `${summary}\n\n详细结果:\n${testResults.join('\n')}\n\n` +
                    `Token长度: ${loginResult.token.length}\n` +
                    `API基础URL: ${NETWORK_CONFIG.API_BASE_URL}`, 
                    allPassed ? 'success' : 'warning'
                );
                
            } catch (error) {
                testResults.push(`❌ 测试异常: ${error.message}`);
                showResult('integrationResult', 
                    `❌ 集成测试失败\n\n${testResults.join('\n')}`, 
                    'error'
                );
            }
        }

        // 页面加载完成后自动检查环境
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
