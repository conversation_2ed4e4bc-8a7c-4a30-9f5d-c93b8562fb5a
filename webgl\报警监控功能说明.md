# 报警监控功能集成说明

## 功能概述

为 `touchmain.html` 文件中的 `alarm-monitor-section` 区域集成了报警设备列表和故障设备列表的API接口功能，实现了实时数据获取、筛选显示和自动刷新。

## 功能特性

### 1. 事件筛选功能
- **所有事件**：显示报警设备列表 + 故障设备列表的合并数据
- **报警事件**：仅显示报警设备列表数据
- **故障事件**：仅显示故障设备列表数据

### 2. 实时数据更新
- 系统每30秒自动获取一次最新数据并更新界面显示
- 数据刷新间隔时间可通过 `config.js` 中的 `CONFIG.DATA_REFRESH_INTERVAL` 参数配置
- 支持手动刷新数据

### 3. 数据显示格式
- **序号**：按时间顺序自动递增（最新事件序号最大）
- **时间格式**：YYYY-MM-DD HH:mm:ss，分为日期和时间两列显示
- **颜色编码**：
  - 报警事件：黄色 (#ffaa00)
  - 故障事件：红色 (#ff4444)
  - 恢复事件：白色 (#ffffff)

## 技术实现

### 1. 配置文件 (config.js)

#### 新增配置参数
```javascript
// 数据刷新间隔时间（毫秒）- 默认30秒
DATA_REFRESH_INTERVAL: 30000
```

#### 新增通用函数
- `getCombinedDeviceList(params)` - 获取合并的设备列表数据
- `formatDeviceEventsForDisplay(deviceList)` - 格式化设备事件数据为显示格式

### 2. 主页面 (touchmain.html)

#### 新增JavaScript功能
- `initAlarmMonitoring()` - 初始化报警监控功能
- `loadAlarmData()` - 加载报警数据
- `updateAlarmDisplay()` - 更新报警显示
- `startAlarmDataRefresh()` - 启动定时刷新
- `stopAlarmDataRefresh()` - 停止定时刷新

#### 数据缓存机制
```javascript
let alarmDataCache = {
    all: [],      // 所有事件数据
    alarm: [],    // 报警事件数据
    fault: [],    // 故障事件数据
    lastUpdate: null  // 最后更新时间
};
```

### 3. 样式文件 (styles.css)

#### 新增样式类
- `.alarm-item` - 报警项样式
- `.alarm-sequence` - 序号样式
- `.alarm-content` - 内容区域样式
- `.alarm-time` - 时间显示样式
- `.alarm-level` - 告警级别样式
- `.alarm-empty` - 空状态样式

## API接口说明

### 1. 报警设备列表接口
- **接口地址**：`/iot/alertLog/list`
- **请求方法**：GET
- **查询参数**：`alertName=报警设备&pageSize=20`

### 2. 故障设备列表接口
- **接口地址**：`/iot/alertLog/list`
- **请求方法**：GET
- **查询参数**：`alertName=故障设备&pageSize=20`

### 3. 认证要求
- 所有API调用需要先通过登录接口获取JWT Token
- Token自动存储在Cookie中，后续请求自动携带

## 使用方法

### 1. 页面加载
页面加载完成后，报警监控功能会自动初始化：
```javascript
// 在DOMContentLoaded事件中自动调用
initAlarmMonitoring();
```

### 2. 筛选切换
用户点击筛选按钮时，会自动切换显示对应类型的事件数据：
- 点击"所有事件"：显示合并的报警和故障数据
- 点击"报警事件"：仅显示报警数据
- 点击"故障事件"：仅显示故障数据

### 3. 自动刷新
系统会根据配置的间隔时间自动刷新数据，无需手动操作。

## 配置参数

### 修改刷新间隔
在 `config.js` 文件中修改：
```javascript
const CONFIG = {
    // 数据刷新间隔时间（毫秒）
    DATA_REFRESH_INTERVAL: 30000,  // 30秒，可根据需要调整
    // ... 其他配置
};
```

### 修改每页数据量
在API调用中修改 `pageSize` 参数：
```javascript
// 在loadAlarmData函数中
const result = await getCombinedDeviceList({ pageSize: 20 }); // 可调整数量
```

## 错误处理

### 1. 网络错误
- 显示错误状态信息
- 自动重试机制
- 用户友好的错误提示

### 2. 认证失败
- 自动检测Token过期
- 提示用户重新登录
- 保持数据状态一致性

### 3. 数据异常
- 空数据友好提示
- 数据格式验证
- 异常数据过滤

## 测试验证

### 测试页面
创建了 `alarm-monitor-test.html` 测试页面，可以：
- 测试API连接
- 验证数据获取
- 测试界面显示
- 调试功能问题

### 测试步骤
1. 打开 `alarm-monitor-test.html`
2. 点击"测试登录"确保认证正常
3. 点击"测试合并列表"验证数据获取
4. 点击"初始化监控"和"加载测试数据"验证界面显示

## 注意事项

1. **依赖关系**：确保 `config.js` 文件在页面中正确加载
2. **网络配置**：确认API服务器地址和端口配置正确
3. **认证状态**：首次使用需要先登录获取有效Token
4. **浏览器兼容性**：使用现代浏览器以确保所有功能正常工作
5. **性能考虑**：大量数据时可能需要调整分页大小和刷新频率

## 维护说明

### 代码结构
- **通用功能**：放在 `config.js` 中，便于复用
- **UI相关功能**：放在 `touchmain.html` 中，与界面紧密相关
- **样式定义**：放在 `styles.css` 中，统一管理

### 扩展建议
- 可以添加更多筛选条件（如时间范围、设备类型等）
- 可以增加数据导出功能
- 可以添加声音或视觉告警提醒
- 可以实现数据统计和趋势分析

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 实现基本的报警监控功能
- 支持三种事件筛选模式
- 实现自动数据刷新
- 添加完整的样式和交互效果
