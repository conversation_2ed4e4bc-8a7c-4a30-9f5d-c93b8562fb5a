<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 200px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>登录功能测试页面</h1>
    
    <div class="test-container">
        <h2>Cookie 管理测试</h2>
        <div class="form-group">
            <label>Cookie名称:</label>
            <input type="text" id="cookieName" value="Admin-Token">
        </div>
        <div class="form-group">
            <label>Cookie值:</label>
            <input type="text" id="cookieValue" value="test-token-123">
        </div>
        <div class="form-group">
            <label>过期天数:</label>
            <input type="number" id="cookieDays" value="7">
        </div>
        <button onclick="testSetCookie()">设置Cookie</button>
        <button onclick="testGetCookie()">获取Cookie</button>
        <button onclick="testDeleteCookie()">删除Cookie</button>
        <div id="cookieResult"></div>
    </div>

    <div class="test-container">
        <h2>登录API测试</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="testUsername" value="bydq_admin">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="testPassword" value="Aa123456">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testInvalidLogin()">测试错误登录</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-container">
        <h2>Token验证测试</h2>
        <div class="form-group">
            <label>Token:</label>
            <input type="text" id="testToken" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mockTokenSignature">
        </div>
        <button onclick="testValidateToken()">验证Token</button>
        <button onclick="testInvalidToken()">测试无效Token</button>
        <div id="tokenResult"></div>
    </div>

    <div class="test-container">
        <h2>集成测试</h2>
        <button onclick="runFullTest()">运行完整测试</button>
        <div id="fullTestResult"></div>
    </div>

    <script>
        // 从主文件中复制的函数
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
            console.log(`Cookie已设置: ${name}=${value}`);
        }

        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        function deleteCookie(name) {
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/`;
            console.log(`Cookie已删除: ${name}`);
        }

        async function performLogin(username, password) {
            try {
                if (username === 'bydq_admin' && password === 'Aa123456') {
                    const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImJ5ZHFfYWRtaW4iLCJpYXQiOjE3MzI1MzQ4MDAsImV4cCI6MTczMzEzOTYwMH0.mockTokenSignature';
                    
                    return {
                        success: true,
                        token: mockToken,
                        message: '登录成功'
                    };
                } else {
                    return {
                        success: false,
                        message: '用户名或密码错误'
                    };
                }
            } catch (error) {
                console.error('登录API调用失败:', error);
                return {
                    success: false,
                    message: '网络连接失败，请检查网络连接'
                };
            }
        }

        async function validateToken(token) {
            try {
                if (!token || token.length < 10) {
                    return false;
                }

                if (token.includes('mockTokenSignature') || token.startsWith('eyJ')) {
                    return true;
                }

                return false;
            } catch (error) {
                console.error('Token验证失败:', error);
                return false;
            }
        }

        // 测试函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function testSetCookie() {
            const name = document.getElementById('cookieName').value;
            const value = document.getElementById('cookieValue').value;
            const days = parseInt(document.getElementById('cookieDays').value);
            
            setCookie(name, value, days);
            showResult('cookieResult', `Cookie "${name}" 已设置，值为 "${value}"，${days}天后过期`, 'success');
        }

        function testGetCookie() {
            const name = document.getElementById('cookieName').value;
            const value = getCookie(name);
            
            if (value) {
                showResult('cookieResult', `Cookie "${name}" 的值为: "${value}"`, 'success');
            } else {
                showResult('cookieResult', `Cookie "${name}" 不存在`, 'error');
            }
        }

        function testDeleteCookie() {
            const name = document.getElementById('cookieName').value;
            deleteCookie(name);
            showResult('cookieResult', `Cookie "${name}" 已删除`, 'success');
        }

        async function testLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            
            showResult('loginResult', '正在测试登录...', 'info');
            
            const result = await performLogin(username, password);
            
            if (result.success) {
                showResult('loginResult', `登录成功！Token: ${result.token.substring(0, 50)}...`, 'success');
            } else {
                showResult('loginResult', `登录失败: ${result.message}`, 'error');
            }
        }

        async function testInvalidLogin() {
            showResult('loginResult', '正在测试错误登录...', 'info');
            
            const result = await performLogin('wrong_user', 'wrong_pass');
            
            if (!result.success) {
                showResult('loginResult', `预期的登录失败: ${result.message}`, 'success');
            } else {
                showResult('loginResult', '错误：应该登录失败但却成功了', 'error');
            }
        }

        async function testValidateToken() {
            const token = document.getElementById('testToken').value;
            
            showResult('tokenResult', '正在验证Token...', 'info');
            
            const isValid = await validateToken(token);
            
            if (isValid) {
                showResult('tokenResult', 'Token验证成功', 'success');
            } else {
                showResult('tokenResult', 'Token验证失败', 'error');
            }
        }

        async function testInvalidToken() {
            showResult('tokenResult', '正在测试无效Token...', 'info');
            
            const isValid = await validateToken('invalid-token');
            
            if (!isValid) {
                showResult('tokenResult', '预期的Token验证失败', 'success');
            } else {
                showResult('tokenResult', '错误：无效Token应该验证失败', 'error');
            }
        }

        async function runFullTest() {
            showResult('fullTestResult', '开始运行完整测试...', 'info');
            
            let results = [];
            
            // 测试1: Cookie管理
            setCookie('test-cookie', 'test-value', 1);
            const cookieValue = getCookie('test-cookie');
            if (cookieValue === 'test-value') {
                results.push('✓ Cookie设置和获取测试通过');
            } else {
                results.push('✗ Cookie设置和获取测试失败');
            }
            
            // 测试2: 正确登录
            const loginResult = await performLogin('bydq_admin', 'Aa123456');
            if (loginResult.success) {
                results.push('✓ 正确登录测试通过');
                
                // 测试3: Token验证
                const tokenValid = await validateToken(loginResult.token);
                if (tokenValid) {
                    results.push('✓ Token验证测试通过');
                } else {
                    results.push('✗ Token验证测试失败');
                }
            } else {
                results.push('✗ 正确登录测试失败');
            }
            
            // 测试4: 错误登录
            const wrongLoginResult = await performLogin('wrong', 'wrong');
            if (!wrongLoginResult.success) {
                results.push('✓ 错误登录测试通过');
            } else {
                results.push('✗ 错误登录测试失败');
            }
            
            // 测试5: 无效Token
            const invalidTokenResult = await validateToken('invalid');
            if (!invalidTokenResult) {
                results.push('✓ 无效Token测试通过');
            } else {
                results.push('✗ 无效Token测试失败');
            }
            
            // 清理测试Cookie
            deleteCookie('test-cookie');
            
            const allPassed = results.every(r => r.startsWith('✓'));
            const resultType = allPassed ? 'success' : 'error';
            const summary = allPassed ? '所有测试通过！' : '部分测试失败！';
            
            showResult('fullTestResult', 
                `<strong>${summary}</strong><br><br>` + results.join('<br>'), 
                resultType
            );
        }
    </script>
</body>
</html>
