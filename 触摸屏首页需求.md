# 触摸屏首页
基于现有的main.html文件，创建一个专门为触摸屏设计的新页面版本页“touch.html”，具体要求如下：

**页面规格：**
- 触摸屏尺寸：1920×1080像素
- 保持现有的深色科技主题和工业监控界面风格
- 优化触摸操作体验，确保按钮和交互元素适合手指触控


具体要求如下：

## 页面结构：

### 顶部
左侧： icon+名称
右侧：登录信息+当前时间
- 登录按钮：点击一下，注销登录，弹出登陆框，输入用户名8，密码262143即可登录成功，否则登录失败。

### 左侧导航栏：
- 电气拓扑：
- 单元状态：
- 版本信息：
- 参数曲线：
- 历史事件：
- 故障录波：
- DSP参数：
- IO状态：
- 水冷系统：
- 调试参数：对应：“/debug1/设备操作.html”
- 权限：登录后可见。点击弹出弹框，可以配置如下菜单是否显示：
    - 参数曲线
    - 历史事件
    - 故障录波
    - DSP参数
    - IO状态
    - 水冷系统
    - 调试参数
- 调试参数2：登录后可见。对应：“/debug1/设备操作.html”

点击事件：
- 以iframe的方式打开新窗口。
- iframe的样式和功能参考 "touchscreen.html"。保留右侧关闭按钮，不要左侧返回上一页的按钮。
- 在点击“调试参数”和“调试参数2”，显示一个菜单按钮，点击导航到对应区域，具体页面在 debug1/ 和 debug2/ 两个目录下。

### 中间
上：「电气系统」system-status-area的status-section
中：网侧负载无功电流图表
下：「电气系统」system-status-area的parameters-section

### 右侧
上：center-panel，去掉unity 3D容器，只保留 <!-- 电气拓扑容器 --> 和 <!-- 水冷拓扑容器 -->
下：<!-- 实时报警监控展示 -->

